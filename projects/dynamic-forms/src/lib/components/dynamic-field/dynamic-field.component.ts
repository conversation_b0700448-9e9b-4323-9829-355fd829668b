import {
  Component,
  Input,
  OnInit,
  ViewChild,
  ViewContainerRef,
  ComponentRef,
  Type,
} from '@angular/core';
import { FormGroup } from '@angular/forms';
import { FieldConfig } from '../../core/models/field-config.interface';
import { FieldType } from '../../core/models/field-type.enum';
import { InputFieldComponent } from '../../field-types/input/input-field.component';
import { SelectFieldComponent } from '../../field-types/select/select-field.component';
import { CheckboxFieldComponent } from '../../field-types/checkbox/checkbox-field.component';
import { BaseFieldComponent } from '../../field-types/base/base-field.component';

@Component({
  selector: 'df-dynamic-field',
  template: `
    <ng-container #fieldContainer></ng-container>
    <div
      *ngIf="!field.hidden && field.type === 'group' && field.fieldGroup"
      class="form-group"
    >
      <fieldset>
        <legend *ngIf="field.label">{{ field.label }}</legend>
        <div [formGroupName]="field.key">
          <df-dynamic-field
            *ngFor="let childField of field.fieldGroup"
            [field]="childField"
            [form]="getFormGroup()"
          ></df-dynamic-field>
        </div>
      </fieldset>
    </div>
  `,
  styles: [
    `
      .form-group {
        margin-bottom: 1rem;
      }

      fieldset {
        border: 1px solid #dee2e6;
        border-radius: 4px;
        padding: 1rem;
        margin-bottom: 1rem;
      }

      legend {
        font-weight: 600;
        padding: 0 0.5rem;
        width: auto;
      }
    `,
  ],
})
export class DynamicFieldComponent implements OnInit {
  @Input() field!: FieldConfig;
  @Input() form!: FormGroup;

  @ViewChild('fieldContainer', { read: ViewContainerRef, static: true })
  fieldContainer!: ViewContainerRef;

  private componentRef!: ComponentRef<BaseFieldComponent>;

  private fieldComponents: { [key: string]: Type<BaseFieldComponent> } = {
    [FieldType.INPUT]: InputFieldComponent,
    [FieldType.EMAIL]: InputFieldComponent,
    [FieldType.PASSWORD]: InputFieldComponent,
    [FieldType.NUMBER]: InputFieldComponent,
    [FieldType.DATE]: InputFieldComponent,
    [FieldType.SELECT]: SelectFieldComponent,
    [FieldType.CHECKBOX]: CheckboxFieldComponent,
    [FieldType.TEXTAREA]: InputFieldComponent,
    [FieldType.RADIO]: SelectFieldComponent, // Using select for now
  };

  ngOnInit(): void {
    if (
      !this.field.hidden &&
      this.field.type !== FieldType.GROUP &&
      this.field.type !== FieldType.ARRAY
    ) {
      this.createFieldComponent();
    }
  }

  private createFieldComponent(): void {
    const componentType = this.fieldComponents[this.field.type];

    if (!componentType) {
      console.error(`Field type "${this.field.type}" is not supported`);
      return;
    }

    this.fieldContainer.clear();
    this.componentRef = this.fieldContainer.createComponent(componentType);
    this.componentRef.instance.field = this.field;
    this.componentRef.instance.form = this.form;
  }

  getFormGroup(): FormGroup {
    return this.field.type === FieldType.GROUP
      ? (this.form.get(this.field.key) as FormGroup)
      : this.form;
  }
}
