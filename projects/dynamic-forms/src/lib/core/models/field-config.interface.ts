import { ValidatorFn, AsyncValidatorFn } from '@angular/forms';
import { FieldType } from './field-type.enum';

export interface FieldConfig {
  key: string;
  type: FieldType;
  label?: string;
  placeholder?: string;
  value?: any;
  required?: boolean;
  disabled?: boolean;
  readonly?: boolean;
  hidden?: boolean;
  options?: OptionItem[]; // For select, radio
  validators?: ValidatorFn[];
  asyncValidators?: AsyncValidatorFn[];
  errorMessages?: { [key: string]: string };
  cssClass?: string;
  hint?: string;
  fieldGroup?: FieldConfig[]; // For group type
  fieldArray?: FieldConfig; // For array type
}

export interface OptionItem {
  label: string;
  value: any;
  disabled?: boolean;
}

export interface DynamicFormConfig {
  fields: FieldConfig[];
  cssClass?: string;
  submitLabel?: string;
  resetLabel?: string;
}
