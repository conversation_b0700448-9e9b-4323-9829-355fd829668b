import { Injectable } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormArray,
  FormControl,
  Validators,
} from '@angular/forms';
import { FieldConfig } from '../models/field-config.interface';
import { FieldType } from '../models/field-type.enum';

@Injectable()
export class FormBuilderService {
  constructor(private readonly fb: FormBuilder) {}

  public createForm(fields: FieldConfig[]): FormGroup {
    const group: any = {};

    fields.forEach((field) => {
      if (field.type === FieldType.GROUP && field.fieldGroup) {
        // Handle nested form group
        group[field.key] = this.createForm(field.fieldGroup);
      } else if (field.type === FieldType.ARRAY && field.fieldArray) {
        // Handle form array
        group[field.key] = this.fb.array([]);
      } else {
        // Handle regular form control
        group[field.key] = this.createControl(field);
      }
    });

    return this.fb.group(group);
  }

  public createControl(field: FieldConfig): FormControl {
    const validators = this.getValidators(field);
    const asyncValidators = field.asyncValidators || [];

    const control = new FormControl(
      {
        value: field.value || null,
        disabled: field.disabled || false,
      },
      validators,
      asyncValidators
    );

    return control;
  }

  private getValidators(field: FieldConfig): any[] {
    const validators = field.validators || [];

    if (field.required) {
      validators.push(Validators.required);
    }

    // Add type-specific validators
    switch (field.type) {
      case FieldType.EMAIL:
        validators.push(Validators.email);
        break;
      case FieldType.NUMBER:
        validators.push(Validators.pattern(/^\d+$/));
        break;
    }

    return validators;
  }

  public addArrayItem(formArray: FormArray, fieldConfig: FieldConfig): void {
    if (fieldConfig.fieldGroup) {
      formArray.push(this.createForm(fieldConfig.fieldGroup));
    } else {
      formArray.push(this.createControl(fieldConfig));
    }
  }

  public removeArrayItem(formArray: FormArray, index: number): void {
    formArray.removeAt(index);
  }

  public updateFieldValue(form: FormGroup, fieldKey: string, value: any): void {
    const control = form.get(fieldKey);
    if (control) {
      control.setValue(value);
      control.markAsTouched();
    }
  }

  public resetForm(form: FormGroup): void {
    form.reset();
    form.markAsUntouched();
  }

  public getFieldErrors(form: FormGroup, fieldKey: string): string[] {
    const control = form.get(fieldKey);
    const errors: string[] = [];

    if (control?.errors && control.touched) {
      Object.keys(control.errors).forEach((key) => {
        errors.push(key);
      });
    }

    return errors;
  }
}
