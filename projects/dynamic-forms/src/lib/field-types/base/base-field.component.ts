import { Component, Input, OnInit } from '@angular/core';
import { FormGroup, AbstractControl } from '@angular/forms';
import { FieldConfig } from '../../core/models/field-config.interface';

@Component({
  template: '',
})
export abstract class BaseFieldComponent implements OnInit {
  @Input() public field!: FieldConfig;
  @Input() public form!: FormGroup;

  public control!: AbstractControl | null;

  public ngOnInit(): void {
    this.control = this.form.get(this.field.key);
  }

  public get isValid(): boolean {
    return this.control ? this.control.valid : true;
  }

  public get isInvalid(): boolean {
    return this.control ? this.control.invalid && this.control.touched : false;
  }

  public get errors(): string[] {
    if (!this.control?.errors || !this.control.touched) {
      return [];
    }

    return Object.keys(this.control.errors).map((key) => {
      if (this.field.errorMessages?.[key]) return this.field.errorMessages[key];
      return this.getDefaultErrorMessage(key);
    });
  }

  private getDefaultErrorMessage(errorKey: string): string {
    const defaultMessages: { [key: string]: string } = {
      required: `${this.field.label || this.field.key} is required`,
      email: 'Please enter a valid email address',
      pattern: 'Please enter a valid format',
      minlength: 'Value is too short',
      maxlength: 'Value is too long', // cspell:ignore maxlength
      min: 'Value is too small',
      max: 'Value is too large',
    };

    return defaultMessages[errorKey] || `Validation error: ${errorKey}`;
  }
}
