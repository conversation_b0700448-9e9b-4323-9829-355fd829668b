import { Component } from '@angular/core';
import { BaseFieldComponent } from '../base/base-field.component';

@Component({
  selector: 'lib-input-field',
  template: `
    <div class="form-field" [ngClass]="field.cssClass">
      <label [for]="field.key" *ngIf="field.label">
        {{ field.label }}
        <span class="required" *ngIf="field.required">*</span>
      </label>

      <input
        [id]="field.key"
        [type]="field.type"
        [formControlName]="field.key"
        [placeholder]="field.placeholder || ''"
        [readonly]="field.readonly"
        class="form-control"
        [class.is-invalid]="isInvalid"
      />

      <small class="hint" *ngIf="field.hint && !isInvalid">
        {{ field.hint }}
      </small>

      <div class="error-messages" *ngIf="isInvalid">
        <small class="error" *ngFor="let error of errors">{{ error }}</small>
      </div>
    </div>
  `,
  styles: [
    `
      .form-field {
        margin-bottom: 1rem;
      }

      label {
        display: block;
        margin-bottom: 0.25rem;
        font-weight: 500;
      }

      .required {
        color: red;
      }

      .form-control {
        width: 100%;
        padding: 0.5rem;
        border: 1px solid #ccc;
        border-radius: 4px;
        font-size: 1rem;
      }

      .form-control:focus {
        outline: none;
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
      }

      .form-control.is-invalid {
        border-color: #dc3545;
      }

      .hint {
        display: block;
        margin-top: 0.25rem;
        color: #6c757d;
        font-size: 0.875rem;
      }

      .error-messages {
        margin-top: 0.25rem;
      }

      .error {
        display: block;
        color: #dc3545;
        font-size: 0.875rem;
      }
    `,
  ],
})
export class InputFieldComponent extends BaseFieldComponent {}
